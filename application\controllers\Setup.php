<?php
defined('BASEPATH') or die('No direct script access allowed!');

class Setup extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        // Only allow access from localhost for security
        if (!in_array($_SERVER['REMOTE_ADDR'], array('127.0.0.1', '::1'))) {
            show_404();
        }
    }

    public function database()
    {
        echo "<h2>Database Setup</h2>";

        try {
            // 1. Create transaction_status_history table
            echo "<p>Creating transaction_status_history table...</p>";

            if (!$this->db->table_exists('transaction_status_history')) {
                $sql = "CREATE TABLE `transaction_status_history` (
                  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                  `transaction_id` int(11) unsigned NOT NULL,
                  `old_status` varchar(50) DEFAULT NULL,
                  `new_status` varchar(50) NOT NULL,
                  `changed_by` int(11) unsigned NOT NULL,
                  `changed_date` datetime NOT NULL,
                  `notes` text DEFAULT NULL,
                  PRIMARY KEY (`id`),
                  <PERSON>EY `transaction_id` (`transaction_id`),
                  <PERSON><PERSON>Y `changed_by` (`changed_by`),
                  KEY `changed_date` (`changed_date`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

                $this->db->query($sql);
                echo "<p style='color: green;'>✓ Table transaction_status_history created successfully</p>";
            } else {
                echo "<p style='color: blue;'>ℹ Table transaction_status_history already exists</p>";
            }

            // 2. Check and fix table structure first
            echo "<p>Checking table structures...</p>";

            // Check transactions table structure
            $transactions_query = $this->db->query("SHOW COLUMNS FROM transactions LIKE 'id'");
            if ($transactions_query->num_rows() > 0) {
                $trans_id_column = $transactions_query->row();
                echo "<p style='color: blue;'>ℹ Transactions.id column type: " . $trans_id_column->Type . "</p>";
            }

            // Check transaction_status_history table structure
            $history_query = $this->db->query("SHOW COLUMNS FROM transaction_status_history LIKE 'transaction_id'");
            if ($history_query->num_rows() > 0) {
                $history_column = $history_query->row();
                echo "<p style='color: blue;'>ℹ transaction_status_history.transaction_id column type: " . $history_column->Type . "</p>";
            }

            // Check msusers table structure
            $users_query = $this->db->query("SHOW COLUMNS FROM msusers LIKE 'id'");
            if ($users_query->num_rows() > 0) {
                $users_id_column = $users_query->row();
                echo "<p style='color: blue;'>ℹ msusers.id column type: " . $users_id_column->Type . "</p>";
            }

            // 3. Add foreign key constraints with proper error handling
            echo "<p>Adding foreign key constraints...</p>";

            // Check if constraint already exists
            $existing_constraints = $this->db->query("
                SELECT CONSTRAINT_NAME
                FROM information_schema.TABLE_CONSTRAINTS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'transaction_status_history'
                AND CONSTRAINT_TYPE = 'FOREIGN KEY'
            ")->result();

            $constraint_names = array();
            foreach ($existing_constraints as $constraint) {
                $constraint_names[] = $constraint->CONSTRAINT_NAME;
            }

            if (!in_array('fk_transaction_status_history_transaction', $constraint_names)) {
                try {
                    $this->db->query("ALTER TABLE `transaction_status_history`
                                     ADD CONSTRAINT `fk_transaction_status_history_transaction`
                                     FOREIGN KEY (`transaction_id`) REFERENCES `transactions`(`id`) ON DELETE CASCADE");
                    echo "<p style='color: green;'>✓ Foreign key constraint for transactions added</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠ Foreign key constraint for transactions failed: " . $e->getMessage() . "</p>";
                    echo "<p style='color: blue;'>ℹ Continuing without foreign key constraint...</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ Foreign key constraint for transactions already exists</p>";
            }

            if (!in_array('fk_transaction_status_history_user', $constraint_names)) {
                try {
                    $this->db->query("ALTER TABLE `transaction_status_history`
                                     ADD CONSTRAINT `fk_transaction_status_history_user`
                                     FOREIGN KEY (`changed_by`) REFERENCES `msusers`(`id`) ON DELETE CASCADE");
                    echo "<p style='color: green;'>✓ Foreign key constraint for users added</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠ Foreign key constraint for users failed: " . $e->getMessage() . "</p>";
                    echo "<p style='color: blue;'>ℹ Continuing without foreign key constraint...</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ Foreign key constraint for users already exists</p>";
            }

            // 4. Add status column to msusers
            echo "<p>Adding status column to msusers table...</p>";

            if (!$this->db->field_exists('status', 'msusers')) {
                $this->db->query("ALTER TABLE `msusers` 
                                 ADD COLUMN `status` ENUM('Pending', 'Aktif', 'Nonaktif') NOT NULL DEFAULT 'Aktif' AFTER `role`");
                echo "<p style='color: green;'>✓ Status column added to msusers table</p>";
            } else {
                echo "<p style='color: blue;'>ℹ Status column already exists in msusers table</p>";
            }

            // 5. Update existing BUMDes users
            echo "<p>Updating existing BUMDes users...</p>";

            $this->db->where('role', 'BUMDes');
            $this->db->where('createdby', 0);
            $this->db->update('msusers', array('status' => 'Pending'));

            $affected_rows = $this->db->affected_rows();
            echo "<p style='color: green;'>✓ Updated $affected_rows BUMDes users to Pending status</p>";

            // 6. Create migrations table if not exists
            echo "<p>Creating migrations table...</p>";

            if (!$this->db->table_exists('migrations')) {
                $sql = "CREATE TABLE `migrations` (
                  `version` bigint(20) NOT NULL,
                  PRIMARY KEY (`version`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

                $this->db->query($sql);
                echo "<p style='color: green;'>✓ Migrations table created</p>";
            } else {
                echo "<p style='color: blue;'>ℹ Migrations table already exists</p>";
            }

            // 7. Insert migration versions
            echo "<p>Inserting migration versions...</p>";

            $this->db->query("INSERT IGNORE INTO `migrations` (`version`) VALUES (20250109000001)");
            $this->db->query("INSERT IGNORE INTO `migrations` (`version`) VALUES (20250109000002)");

            echo "<p style='color: green;'>✓ Migration versions inserted</p>";

            echo "<h3 style='color: green;'>Database setup completed successfully!</h3>";
            echo "<p><a href='" . base_url() . "'>Go to Dashboard</a></p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
    }

    public function simple()
    {
        echo "<h2>Simple Database Setup (Without Foreign Keys)</h2>";

        try {
            // 1. Create transaction_status_history table without foreign keys
            echo "<p>Creating transaction_status_history table...</p>";

            if (!$this->db->table_exists('transaction_status_history')) {
                $sql = "CREATE TABLE `transaction_status_history` (
                  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                  `transaction_id` int(11) unsigned NOT NULL,
                  `old_status` varchar(50) DEFAULT NULL,
                  `new_status` varchar(50) NOT NULL,
                  `changed_by` int(11) unsigned NOT NULL,
                  `changed_date` datetime NOT NULL,
                  `notes` text DEFAULT NULL,
                  PRIMARY KEY (`id`),
                  KEY `transaction_id` (`transaction_id`),
                  KEY `changed_by` (`changed_by`),
                  KEY `changed_date` (`changed_date`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

                $this->db->query($sql);
                echo "<p style='color: green;'>✓ Table transaction_status_history created successfully</p>";
            } else {
                echo "<p style='color: blue;'>ℹ Table transaction_status_history already exists</p>";
            }

            // 2. Add status column to msusers
            echo "<p>Adding status column to msusers table...</p>";

            if (!$this->db->field_exists('status', 'msusers')) {
                $this->db->query("ALTER TABLE `msusers`
                                 ADD COLUMN `status` ENUM('Pending', 'Aktif', 'Nonaktif') NOT NULL DEFAULT 'Aktif' AFTER `role`");
                echo "<p style='color: green;'>✓ Status column added to msusers table</p>";
            } else {
                echo "<p style='color: blue;'>ℹ Status column already exists in msusers table</p>";
            }

            // 3. Update existing BUMDes users
            echo "<p>Updating existing BUMDes users...</p>";

            $this->db->where('role', 'BUMDes');
            $this->db->where('createdby', 0);
            $this->db->update('msusers', array('status' => 'Pending'));

            $affected_rows = $this->db->affected_rows();
            echo "<p style='color: green;'>✓ Updated $affected_rows BUMDes users to Pending status</p>";

            echo "<h3 style='color: green;'>Simple database setup completed successfully!</h3>";
            echo "<p><strong>Note:</strong> Foreign key constraints were skipped to avoid compatibility issues.</p>";
            echo "<p><a href='" . base_url() . "'>Go to Dashboard</a></p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
        }
    }

    public function check()
    {
        echo "<h2>Database Check</h2>";

        // Check if transaction_status_history table exists
        if ($this->db->table_exists('transaction_status_history')) {
            echo "<p style='color: green;'>✓ transaction_status_history table exists</p>";

            // Check table structure
            $columns = $this->db->query("SHOW COLUMNS FROM transaction_status_history")->result();
            echo "<p>Table structure:</p><ul>";
            foreach ($columns as $column) {
                echo "<li>{$column->Field} - {$column->Type}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ transaction_status_history table does not exist</p>";
        }

        // Check if status column exists in msusers
        if ($this->db->field_exists('status', 'msusers')) {
            echo "<p style='color: green;'>✓ status column exists in msusers table</p>";
        } else {
            echo "<p style='color: red;'>❌ status column does not exist in msusers table</p>";
        }

        // Check migrations table
        if ($this->db->table_exists('migrations')) {
            echo "<p style='color: green;'>✓ migrations table exists</p>";

            $migrations = $this->db->get('migrations')->result();
            echo "<p>Migration versions:</p><ul>";
            foreach ($migrations as $migration) {
                echo "<li>" . $migration->version . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ migrations table does not exist</p>";
        }

        echo "<p><a href='" . base_url('setup/database') . "'>Run Full Database Setup</a></p>";
        echo "<p><a href='" . base_url('setup/simple') . "'>Run Simple Database Setup (Recommended)</a></p>";
    }
}
