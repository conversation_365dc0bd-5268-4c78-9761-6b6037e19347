-- SQL untuk membuat tabel transaction_status_history dan menambahkan kolom status ke msusers
-- VERSI SEDERHANA TANPA FOREIGN KEY CONSTRAINTS

-- 1. Buat tabel transaction_status_history (tanpa foreign key constraints)
CREATE TABLE IF NOT EXISTS `transaction_status_history` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `transaction_id` int(11) unsigned NOT NULL,
  `old_status` varchar(50) DEFAULT NULL,
  `new_status` varchar(50) NOT NULL,
  `changed_by` int(11) unsigned NOT NULL,
  `changed_date` datetime NOT NULL,
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_id` (`transaction_id`),
  KEY `changed_by` (`changed_by`),
  KEY `changed_date` (`changed_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. Tambahkan kolom status ke tabel msusers jika belum ada
ALTER TABLE `msusers`
ADD COLUMN `status` ENUM('Pending', 'Aktif', 'Nonaktif') NOT NULL DEFAULT 'Aktif' AFTER `role`;

-- 3. Update existing BUMDes users yang dibuat melalui self-registration (createdby = 0) ke status Pending
UPDATE `msusers`
SET `status` = 'Pending'
WHERE `role` = 'BUMDes' AND `createdby` = 0;

-- 4. Buat tabel migrations jika belum ada (untuk tracking migration)
CREATE TABLE IF NOT EXISTS `migrations` (
  `version` bigint(20) NOT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5. Insert migration version
INSERT IGNORE INTO `migrations` (`version`) VALUES (20250109000001);
INSERT IGNORE INTO `migrations` (`version`) VALUES (20250109000002);

-- CATATAN: Foreign key constraints dihilangkan untuk menghindari masalah kompatibilitas.
-- Aplikasi akan tetap berfungsi normal tanpa foreign key constraints.
